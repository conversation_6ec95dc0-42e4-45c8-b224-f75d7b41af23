import { grpMbrs } from './grp-mbrs/grp-mbrs.js'
import { errs } from './errs/errs.js'
import { passkeys } from './passkeys/passkeys.js'
import { challenges } from './challenges/challenges.js'
import { expenses } from './expenses/expenses.js'
import { healthShares } from './health-shares/health-shares.js'
import { aiChats } from './ai-chats/ai-chats.js'
import { gpps } from './gpps/gpps.js'
import { gps } from './gps/gps.js'
import { fbRes } from './fb-res/fb-res.js'
import { fbs } from './fbs/fbs.js'
import { sePlans } from './se-plans/se-plans.js'
import { wallets } from './wallets/wallets.js'
import { funds } from './funds/funds.js'
import { mbrs } from './mbrs/mbrs.js'
import { teams } from './teams/teams.js'
import { calendars } from './calendars/calendars.js'
import { ims } from './ims/ims.js'
import { shops } from './shops/shops.js'
import { billErasers } from './bill-erasers/bill-erasers.js'
import { priceEstimates } from './price-estimates/price-estimates.js'
import { offers } from './offers/offers.js'
import { contracts } from './contracts/contracts.js'
import { cobras } from './cobras/cobras.js'
import { drops } from './drops/drops.js'
import { networks } from './networks/networks.js'
import { caps } from './caps/caps.js'
import { bundles } from './bundles/bundles.js'
import { claimReqs } from './claim-reqs/claim-reqs.js'
import { fundsRequests } from './funds-requests/funds-requests.js'
import { careAccounts } from './care-accounts/care-accounts.js'
import { budgets } from './budgets/budgets.js'
import { hosts } from './hosts/hosts.js'
import { markets } from './markets/markets.js'
import { rates } from './rates/rates.js'
import { banking } from './banking/banking.js'
import { ledgers } from './ledgers/ledgers.js'
import { crossSections } from './cross-sections/cross-sections.js'
import { bankAccounts } from './bank-accounts/bank-accounts.js'
import { claims } from './claims/claims.js'
import { salesTaxes } from './sales-taxes/sales-taxes.js'
import { prices } from './prices/prices.js'
import { claimPayments } from './claim-payments/claim-payments.js'
import { bills } from './bills/bills.js'
import { visits } from './visits/visits.js'
import { practitioners } from './practitioners/practitioners.js'
import { meds } from './meds/meds.js'
import { conditions } from './conditions/conditions.js'
import { providers } from './providers/providers.js'
import { cares } from './cares/cares.js'
import { specs } from './specs/specs.js'
import { docTemplates } from './doc-templates/doc-templates.js'
import { planDocs } from './plan-docs/plan-docs.js'
import { marketplace } from './marketplace/marketplace.js'
import { households } from './households/households.js'
import { enrollments } from './enrollments/enrollments.js'
import { coverages } from './coverages/coverages.js'
import { procedures } from './procedures/procedures.js'
import { plans } from './plans/plans.js'
import { cams } from './cams/cams.js'
import { comps } from './comps/comps.js'
import { crypto } from './crypto/crypto.js'
import { groups } from './groups/groups.js'
import { junkDrawers } from './junk-drawers/junk-drawers.js'
import { issues } from './issues/issues.js'
import { changeLogs } from './change-logs/change-logs.js'
import { cats } from './cats/cats.js'
import { reqs } from './reqs/reqs.js'
import { refs } from './refs/refs.js'
import { logins } from './logins/logins.js'
import { fingerprints } from './fingerprints/fingerprints.js'
import { myIp } from './my-ip/my-ip.js'
import { batch } from './batch/batch.js'
import { authManagement } from './auth-management/auth-management.js'
import { ppls } from './ppls/ppls.js'
import { orgs } from './orgs/orgs.js'
import { uploads } from './uploads/uploads.js'
import { tomtomReverseGeocode } from './tomtom-reverse-geocode/tomtom-reverse-geocode.js'
import { tomtomGeocode } from './tomtom-geocode/tomtom-geocode.js'
import { threads } from './threads/threads.js'
import { leads } from './leads/leads.js'
import { docRequests } from './doc-requests/doc-requests.js'
import { flowCharts } from './flow-charts/flow-charts.js'
import { ucans } from './ucans/ucans.js'
import { servicesExpose } from './services-expose/services-expose.js'
import { pings } from './pings/pings.js'

// For more information about this file see https://dove.feathersjs.com/guides/cli/application.html#configure-functions
import type { Application } from '../declarations.js'

export const services = (app: Application) => {
  app.configure(grpMbrs)
  app.configure(errs)
  app.configure(passkeys)
  app.configure(challenges)
  app.configure(expenses)
  app.configure(healthShares)
  app.configure(aiChats)
  app.configure(gpps)
  app.configure(gps)
  app.configure(comps)
  app.configure(fbRes)
  app.configure(fbs)
  app.configure(sePlans)
  app.configure(wallets)
  app.configure(funds)
  app.configure(mbrs)
  app.configure(teams)
  app.configure(calendars)
  app.configure(ims)
  app.configure(shops)
  app.configure(billErasers)
  app.configure(priceEstimates)
  app.configure(offers)
  app.configure(contracts)
  app.configure(cobras)
  app.configure(drops)
  app.configure(networks)
  app.configure(caps)
  app.configure(bundles)
  app.configure(claimReqs)
  app.configure(fundsRequests)
  app.configure(careAccounts)
  app.configure(budgets)
  app.configure(hosts)
  app.configure(markets)
  app.configure(rates)
  app.configure(banking)
  app.configure(ledgers)
  app.configure(crossSections)
  app.configure(bankAccounts)
  app.configure(claims)
  app.configure(salesTaxes)
  app.configure(prices)
  app.configure(claimPayments)
  app.configure(bills)
  app.configure(visits)
  app.configure(practitioners)
  app.configure(meds)
  app.configure(conditions)
  app.configure(providers)
  app.configure(cares)
  app.configure(specs)
  app.configure(docTemplates)
  app.configure(planDocs)
  app.configure(marketplace)
  app.configure(households)
  app.configure(enrollments)
  app.configure(coverages)
  app.configure(procedures)
  app.configure(plans)
  app.configure(cams)
  app.configure(crypto)
  app.configure(groups)
  app.configure(junkDrawers)
  app.configure(issues)
  app.configure(changeLogs)
  app.configure(uploads)
  app.configure(tomtomReverseGeocode)
  app.configure(tomtomGeocode)
  app.configure(threads)
  app.configure(reqs)
  app.configure(refs)
  app.configure(ppls)
  app.configure(orgs)
  app.configure(myIp)
  app.configure(logins)
  app.configure(leads)
  app.configure(docRequests)
  app.configure(batch)
  app.configure(authManagement)
  app.configure(fingerprints)
  app.configure(flowCharts)
  app.configure(cats)
  app.configure(ucans)
  app.configure(servicesExpose)
  app.configure(pings)
  // All services will be registered here
}
