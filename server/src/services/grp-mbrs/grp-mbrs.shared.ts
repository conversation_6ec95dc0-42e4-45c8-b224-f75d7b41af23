// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { GrpMbrs, GrpMbrsData, GrpMbrsPatch, GrpMbrsQuery, GrpMbrsService } from './grp-mbrs.class.js'

export type { GrpMbrs, GrpMbrsData, GrpMbrsPatch, GrpMbrsQuery }

export type GrpMbrsClientService = Pick<GrpMbrsService<Params<GrpMbrsQuery>>, (typeof grpMbrsMethods)[number]>

export const grpMbrsPath = 'grp-mbrs'

export const grpMbrsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const grpMbrsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(grpMbrsPath, connection.service(grpMbrsPath), {
    methods: grpMbrsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [grpMbrsPath]: GrpMbrsClientService
  }
}
