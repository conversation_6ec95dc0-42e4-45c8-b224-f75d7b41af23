<template>
  <div class="_fw">

    <div class="flex items-center q-pa-sm">
      <div class="tw-six font-1r">Manage Enrollments</div>
      <q-btn dense flat icon="mdi-plus" color="primary" no-caps @click="adding = year">
      </q-btn>
    </div>

    <common-dialog setting="smmd" :model-value="adding > 0" @update:model-value="closeAdd">
      <div class="bg-white _fw q-pa-md">

        <div class="q-pa-sm tw-six text-primary font-1r">Add Enrollment</div>
        <q-checkbox :model-value="adding === year" :label="String(year)"
                    @update:model-value="adding = year"></q-checkbox>
        <q-checkbox :model-value="adding !== year" :label="String(Number(year)+1)"
                    @update:model-value="adding=year+1"></q-checkbox>
        <div class="row items-center justify-end">
          <q-btn no-caps @click="adding = 0" flat>
            <span>Cancel</span>
            <q-icon class="q-ml-sm" name="mdi-close" color="red"></q-icon>
          </q-btn>
          <q-btn
              flat
              no-caps
              @click="addNextKey(adding)">
            <span>Add Enrollment {{ nextKey(adding)?.split('_').join(' - ') }}</span>
            <q-icon class="q-ml-sm" name="mdi-check" color="green"></q-icon>
          </q-btn>
        </div>


      </div>
    </common-dialog>

    <q-chip class="tw-six" color="transparent" clickable icon-right="mdi-menu-down" :label="year">
      <q-menu class="w200 bg-white">
        <q-list>
          <q-item v-for="(k, i) in Object.keys(enrollments || {})" :key="`k-${i}`" clickable @click="setYear(k)">
            <q-item-section>
              <q-item-label>{{ k }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-chip>

    <plan-year-enrollments :plan="fullPlan" :year="year"></plan-year-enrollments>

  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PlanYearEnrollments from 'components/plans/enrollments/cards/PlanYearEnrollments.vue';

  import {computed, onMounted, ref} from 'vue';
  import {enrollmentsByYear} from '../utils';
  import {idGet} from 'src/utils/id-get';

  import {usePlans} from 'stores/plans';
  import {useRoute, useRouter} from 'vue-router';
  import {useEnvStore} from 'stores/env';

  const envStore = useEnvStore()
  const planStore = usePlans();

  const route = useRoute();
  const router = useRouter();

  const emit = defineEmits(['add']);
  const props = defineProps({
    plan: { required: false }
  })

  const yr = new Date().getFullYear();
  const year = ref(yr);

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || envStore.getPlanId),
    routeParamsPath: 'planId'
  })
  const enrollments = computed(() => enrollmentsByYear(fullPlan.value));

  const adding = ref(0);

  const closeAdd = (val) => {
    if (!val) {
      adding.value = 0;
    }
  }

  const nextKey = (y) => {
    const keys = Object.keys(enrollments.value[y] || {});
    const last = keys ? Number(keys[keys.length - 1] || -1) : -1;
    return `${y}_${last + 1}`
  }
  const addNextKey = (y) => {
    // console.log('add next key', y)
    const nk = nextKey(y);
    emit('add', nk);
    planStore.patchInStore(fullPlan.value._id, { enrollments: { ...fullPlan.value.enrollments, [nk]: {} } })
    closeAdd()
    router.push({...route, params: { year: nk.split('_')[0], tab: 'events' }})
  }

  const setYear = (y) => {
    router.push({ ...route, params: { year: y } })
    year.value = Number(y)
  }

  onMounted(() => {
    if (route.params.year) year.value = Number(route.params.year)
  })

</script>

<style lang="scss" scoped>

</style>
