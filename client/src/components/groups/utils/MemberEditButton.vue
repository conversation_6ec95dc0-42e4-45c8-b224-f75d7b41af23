<template>
  <q-btn
      v-bind="{
        icon: 'mdi-dots-vertical',
        flat: true,
        dense: true,
        ...$attrs
      }"
  >
    <q-menu>
      <div class="__meb q-pa-sm">
        <q-list separator>
          <q-item v-for="(item, i) in items" :key="`item-${i}`" @click="item.click()" clickable>
            <q-item-section>
              <q-item-label class="text-black">
                {{ item.label }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-icon :color="item.color" :name="item.icon"></q-icon>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>

    <common-dialog v-model="editDialog" setting="small">
      <div class="__form q-pa-md">
        <member-input @update:model-value="editDialog=false" :group-id="groupId" :model-value="modelValue"></member-input>
      </div>
    </common-dialog>

    <common-dialog v-model="removeDialog" setting="small">
      <div class="__form q-pa-md">
        <div class="font-1r text-weight-bold">Are you sure you want to remove {{modelValue?.name}} from this group?</div>
        <div class="row q-py-md justify-end">
          <q-btn size="sm" outline color="black" icon-right="mdi-close" label="Cancel" no-caps class="q-mr-sm" @click="removeDialog = false"></q-btn>
          <q-btn size="sm" push color="red" icon-right="mdi-delete" label="Remove" no-caps @click="remove"></q-btn>
        </div>
      </div>
    </common-dialog>

  </q-btn>
</template>

<script setup>
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import MemberInput from 'src/components/groups/forms/MemberInput.vue';
  import {computed, ref} from 'vue';

  import {$errNotify} from 'src/utils/global-methods';
  import {useGrpMbrs} from 'stores/grp-mbrs';
  const mbrStore = useGrpMbrs();

  const emit = defineEmits(['edit']);
  const props = defineProps({
    modelValue: { required: true },
    groupId: { required: true }
  })

  const editDialog = ref(false);
  const removeDialog = ref(false);

  const items = computed(() => [
    {
      label: 'View',
      icon: 'mdi-eye',
      color: 'ir-purple-8'
    },
    {
      label: 'Edit',
      icon: 'mdi-pencil',
      color: 'ir-blue-8',
      click: () => {
        editDialog.value = true
      }
    },
    {
      label: 'Remove From Group',
      icon: 'mdi-close',
      color: 'red',
      click: () => {
        removeDialog.value = true;
      }
    }
  ])

  const remove = async () => {
    const mbr = await mbrStore.find({ query: { $limit: 1, mbrId: `${props.groupId}:${props.modelValue._id}` }})
        .catch(err => {
          $errNotify(`Error removing: ${err.message}`)
          console.error(`Error removing group member: ${err.message}`)
        })
    if(mbr.data.length) mbrStore.remove(mbr.data[0]._id, { disableSoftDelete: true })
  };
</script>

<style lang="scss" scoped>
  .__meb {
    width: 250px;
    color: white;
  }
  .__form {
    width: 100%;
    border-radius: 12px;
    overflow: hidden;
  }
</style>
