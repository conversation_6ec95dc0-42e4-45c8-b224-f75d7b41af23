<template>
  <div>
    <div class="_form_grid">
      <div class="_form_label">First Name</div>
      <div class="q-pa-sm">
        <q-input :model-value="form.name.firstName" @update:model-value="addName($event, 'firstName')"></q-input>
      </div>
      <div class="_form_label">Last Name</div>
      <div class="q-pa-sm">
        <q-input :model-value="form.name.lastName" @update:model-value="addName($event, 'lastname')"></q-input>
      </div>
      <div class="_form_label">SSN</div>
      <div class="q-pa-sm">
        <q-checkbox label="Is ITIN number?" :model-value="isITIN" @update:model-value="toggleITIN"></q-checkbox>
        <ssn-input :model-value="form.governmentID.ssn?.full ? form.governmentID.ssn.full : form.governmentID.itin?.full" @update:model-value="addSsn"></ssn-input>
      </div>
      <div class="_form_label">Phone</div>
      <div class="q-pa-sm">
        <account-contact :display-fn="(val) => val?.number?.e164" :label="form.phone?.number || 'Add Phone'"
                         @update:model-value="addPhone" :list="fullPerson?.phones">
          <template v-slot:input>
            <phone-input
                :model-value="undefined"
                :input-attrs="{ placeholder: 'Add Phone' }"
                @update:model-value="addPhone"></phone-input>
          </template>
        </account-contact>
      </div>
      <div class="_form_label">Email</div>
      <div class="q-pa-sm">
        <account-contact :label="form.email || 'Add Email'" @update:model-value="addEmail" :list="fullPerson?.emails">
          <template v-slot:input>
            <email-field @update:model-value="addEmail"></email-field>
          </template>
        </account-contact>
      </div>
      <div class="_form_label">Address</div>
      <div class="q-pa-sm">
        <account-contact
            :display-fn="(val) => val?.formatted"
            :label="form.address ? moovAddressFormatted(form.address) : 'Add Address'"
            @update:model-value="addAddress"
            :list="fullPerson?.addresses"
        >
          <template v-slot:input>
            <tomtom-autocomplete @update:model-value="addAddress"></tomtom-autocomplete>
          </template>
        </account-contact>
      </div>
      <div class="_form_label">Date of Birth</div>
      <div class="q-pa-sm">
        <dob-input @update:model-value="addDob" :model-value="getBDay(form.birthDate)"></dob-input>
      </div>
      <div class="_form_label">Roles</div>
      <div class="q-pa-sm">
        <q-checkbox label="Banking Controller" :model-value="!!form.responsibilities?.isController"
                    @update:model-value="updateController($event)"></q-checkbox>

      </div>
      <div class="_form_label">Percent Ownership</div>
      <div class="q-pa-sm">
        <money-input
            hide-bottom-space
            v-model="form.responsibilities.ownershipPercentage"
            :decimal="2"
            prefix=""
            @blur="checkPercent"
            suffix="%"
            :hint="over ? 'Total ownership cannot exceed 100%' : ''"
        ></money-input>
      </div>
      <div class="_form_label">Title</div>
      <div class="q-pa-sm">
        <title-chip
            :model-value="form.responsibilities?.jobTitle"
            @update:model-value="setRelationship($event, 'jobTitle', 'position')"
            v-bind="{
              editing: true,
              size: 'md',
              color: 'ir-grey-4',
              iconRight: 'mdi-menu-down'
            }"
        ></title-chip>
      </div>
    </div>
    <div class="row justify-end">
      <q-btn push class="_p_btn" no-caps v-if="Object.keys(diff).length || dirty" @click="save">
        <span>Save</span>
        <q-spinner class="q-ml-sm" color="white" v-if="loading"></q-spinner>
      </q-btn>
    </div>
  </div>
</template>

<script setup>
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import AccountContact from 'components/accounts/treasury/components/forms/AccountContact.vue';
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import DobInput from 'components/common/input/DobInput.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import TitleChip from 'components/orgs/owners/TitleChip.vue';
  import SsnInput from 'components/common/input/SsnInput.vue';

  import {computed, ref, watch} from 'vue';
  import {_get, _set} from 'symbol-syntax-utils';
  import {usePpls} from 'stores/ppls';
  import {useOrgs} from 'stores/orgs';
  import {useBanking} from 'stores/banking';
  import {$errNotify} from 'src/utils/global-methods';
  import {
    addressToMoov,
    moovDob,
    getDiff,
    moovAddressFormatted,
    runMoovPersonMap
  } from '../../utils/moov-utils';

  const pplStore = usePpls();
  const orgStore = useOrgs();
  const bankStore = useBanking();

  const emit = defineEmits(['update:model-value', 'update:account']);
  const props = defineProps({
    org: { required: true },
    account: { required: true },
    person: { required: true },
    acctPerson: { required: false },
    controller: { required: false }
  })

  const fullOrg = computed(() => props.org);
  const fullAccount = computed(() => props.account);
  const fullPerson = computed(() => props.person);

  const patchPaths = ref([]);
  const formFn = (defs) => {
    return {
      name: {},
      birthDate: {},
      governmentID: { ssn: {} },
      address: {},
      phone: {},
      ...defs,
      responsibilities: {
        ownershipPercentage: 0,
        jobTitle: 'officer',
        isOwner: false,
        isController: false,
        ...defs?.responsibilities
      },
    }
  }
  const form = ref(formFn());
  const accountPerson = ref({});
  const allPeople = ref({ data: [] });

  const dirty = ref(false);
  const diff = computed(() => {
    return getDiff(form.value, accountPerson.value)
  })

  const personObj = ref({ $set: {} });
  const pto = ref();
  const maybeSavePerson = async () => {
    clearTimeout(pto);
    pto.value = setTimeout(async () => {
      const prsn = await pplStore.patch(fullPerson.value._id, personObj.value);
      form.value = formFn(runMoovPersonMap({ person: prsn, accountPerson: accountPerson.value, org: fullOrg.value }));
    }, 1500);
  }
  const setPerson = (val, path) => {
    personObj.value.$set[path] = val;
    const obj = _set({}, path, val);
    pplStore.patchInStore(fullPerson.value._id, obj);
    maybeSavePerson();
    dirty.value = true
  }

  const orgObj = ref({ $set: {} });
  const oto = ref();
  const maybeSaveOrg = () => {
    clearTimeout(oto.value);
    oto.value = setTimeout(() => {
      orgStore.patch(fullOrg.value._id, orgObj.value);
      orgObj.value = { $set: {} }
    }, 1500);
  }
  const setOrg = (val, path) => {
    orgObj.value.$set[path] = val;
    const obj = _set({}, path, val);
    orgStore.patchInStore(fullOrg.value._id, obj);
    maybeSaveOrg()
    dirty.value = true
  }

  const setAccountPerson = (p) => {
    accountPerson.value = p
  }
  watch(fullPerson, async (nv) => {
    if (nv) {
      setTimeout(async () => {
        if (props.acctPerson) setAccountPerson(props.acctPerson);
        else {
          console.log('saw full person')
          const personId = (nv.moovAccounts || {})[fullAccount.value?.accountID]?.id;
          if (personId) {
            const ap = await bankStore.get(personId, {
              banking: {
                moov: {
                  method: 'get_representative',
                  args: [fullAccount.value.accountID, personId]
                }
              }
            })
            setAccountPerson(ap);
          }
          if (!accountPerson.value?.representativeID) {
            allPeople.value = await bankStore.get(fullAccount.value.accountID, {
              banking: {
                moov: {
                  method: 'list_representatives',
                  args: [fullAccount.value.accountID]
                }
              }
            })
            const id = (nv.moovAccounts || {})[fullAccount.value.accountID]?.id;
            if(id) {
              for await (const p of allPeople.value?.data || []) {
                if (p.representativeID === id || (p.name?.firstName === nv.firstName && p.name?.lastName === nv.lastName)) {
                  setAccountPerson(p)
                  await pplStore.patch(fullPerson.value._id, {
                    $set: {
                      [`moovAccounts.${fullAccount.value.accountID}`]: {
                        id: p.representativeID,
                        isController: !!p.responsibilities?.isController
                      }
                    }
                  })
                  break;
                }
              }
            }
          }
        }
        console.log('setting form', nv)
        form.value = formFn(runMoovPersonMap({ person: nv, accountPerson: accountPerson.value, org: fullOrg.value }))
      }, 750)
    }
  }, { immediate: true })

  // watch(accountPerson, async (nv, ov) => {
  //   if(nv && nv.representativeID !== ov?.representativeID){
  //     form.value = formFn(runMoovPersonMap({ person: fullPerson.value, accountPerson: nv, org: fullOrg.value }));
  //   }
  // }, { immediate: true })


  watch(() => props.controller, (nv) => {
    if (nv) form.value.responsibilities = { ...form.value.responsibilities, isController: true }
  }, { immediate: true })

  const addName = (val, path) => {
    patchPaths.value.push('name');
    form.value.name = { ...form.value.name, [path]: val}
    setPerson(val, path)
    dirty.value = true
  }

  const setItin = ref(false);
  const isITIN = computed(() => setItin.value || !!form.value?.governmentID?.itin)

  const toggleITIN = (val) => {
    setItin.value = val;
    if(val) {
      form.value.governmentID.itin = form.value.governmentID.ssn || { full: '' }
      delete form.value.governmentID.ssn
    }
    else {
      form.value.governmentID.ssn = form.value.governmentID.itin || { full: '' }
      delete form.value.governmentID.itin
    }
  }

  const addSsn = (val) => {
    const len = val.length;
    const path = isITIN.value ? 'itin' : 'ssn'
    if (len === 11) {
      patchPaths.value.push(`governmentID.${path}`);
      const last4 = val.slice(val.length - 4)
      form.value.governmentID = _set(form.value.governmentID, path, { val, last4 });
      setPerson(val, 'ssn')
      dirty.value = true
    }
  }

  const addPhone = (val) => {
    if (val && form.value.phone?.number !== val.number.e164.slice(2)) {
      patchPaths.value.push('phone');
      form.value.phone = { number: val.number.e164.slice(2), countryCode: String(val.countryCode) };
      const idx = (fullPerson.value.phones || []).map(a => a.number.e164).indexOf(val.number.e164);
      if (idx === -1) {
        pplStore.patchInStore(fullPerson.value._id, { phones: [...fullPerson.value.phones || [], val] });
        personObj.value.$addToSet = { phones: val }
        maybeSavePerson();
      }
      dirty.value = true
    }
  }

  const addEmail = (val) => {
    if (val && form.value.email !== val) {
      patchPaths.value.push('email');
      form.value.phone = val
      const idx = (fullPerson.value.emails || []).indexOf(val);
      if (idx === -1) {
        pplStore.patchInStore(fullPerson.value._id, { emails: [...fullPerson.value.emails || [], val] });
        personObj.value.$addToSet = { emails: val }
        maybeSavePerson();
      }
      dirty.value = true
    }
  }

  const addAddress = (val) => {
    if (val) {
      patchPaths.value.push('address');
      form.value.address = addressToMoov(val);
      const idx = (fullPerson.value.addresses || []).map(a => a.formatted).indexOf(val.formatted);
      if (idx === -1) {
        pplStore.patchInStore(fullPerson.value._id, { addresses: [...fullPerson.value.addresses || [], val] });
        personObj.value.$addToSet = { addresses: val }
        maybeSavePerson();
      }
      dirty.value = true
    }
  }

  const addDob = (val) => {
    if (val) {
      patchPaths.value.push('birthDate');
      form.value.birthDate = moovDob(val);
      setPerson(val, 'dob');
      dirty.value = true
    }
  }

  const getBDay = (val) => {
    if (!val) return undefined;
    return new Date(`${val.year}-${val.month}-${val.day}`)
  }

  const over = ref(false);
  const setRelationship = (val, path, orgPath) => {
    patchPaths.value.push('responsibilities');
    form.value.responsibilities = { ...form.value.responsibilities, [path]: val }
    const list = [...fullOrg.value.owners];
    const idx = list.map(a => a.id).indexOf(fullPerson.value._id)
    if (idx > -1) {
      list[idx][orgPath] = val
      setOrg(list, 'owners')
    }
    dirty.value = true
  }

  const checkPercent = async () => {
    const percent = Number(form.value.responsibilities.ownershipPercentage);

    const existing = (fullOrg.value?.owners || []).filter(a => a.id !== props.person?._id).reduce((acc, v) => acc + (v.percent || 0), 0);

    const apply = existing + percent > 100 ? 100 - existing : percent;
    if(apply !== percent) over.value = true;
    if(percent !== props.acctPerson?.responsibilities?.ownershipPercentage){
        setRelationship(apply, 'ownershipPercentage', 'percent')
    }
    const idx = (fullOrg.value?.owners || []).map(a => a.id).indexOf(props.person._id);
    if(percent !== (fullOrg.value.owners || [])[idx]?.percent){
      const owners = [...fullOrg.value.owners || []]
      if(idx > -1) owners.splice(idx, 1, { ...owners[idx], percent: apply })
      else owners.push({ id: props.person._id, idService: 'ppls', percent: apply })
      await orgStore.patch(fullOrg.value._id, { owners })
    }
  }

  const updateController = (val) => {
    form.value.responsibilities = { ...form.value.responsibilities, isController: val }
  }

  const loading = ref(false);
  const save = async () => {
    const accountId = fullAccount.value.accountID;
    let error = false;
    loading.value = true
    if (!accountPerson.value?.representativeID) {
      const p = await bankStore.get(accountId, {
        banking: {
          moov: {
            method: 'create_representative',
            args: [accountId, fullPerson.value._id, form.value]
          }
        }
      })
          .catch(err => {
            error = true;
            $errNotify(`Error adding person: ${err.message}`)
            console.error(err.message);
          })
      setAccountPerson(p)
    } else {
      let obj = {};
      for (const p of patchPaths.value) {
        obj = _set(obj, p, _get(form.value, p));
      }
      for (const k in diff.value) {
        const val = _get(form.value, k);
        if (typeof val !== 'undefined') obj = _set(obj, k, val);
      }
      const p = await bankStore.get(accountId, {
        banking: {
          moov: {
            method: 'update_representative',
            args: [accountId, accountPerson.value.representativeID, obj]
          }
        }
      })
          .catch(err => {
            error = true;
            $errNotify(`Error updating person: ${err.message}`)
            console.error(err.message);
          })
      setAccountPerson(p)
    }
    loading.value = false
    if (!error) emit('update:model-value', accountPerson.value);

  }


</script>

<style lang="scss" scoped>

</style>
