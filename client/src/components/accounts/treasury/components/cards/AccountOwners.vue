<template>
  <div class="_fw">
    <div class="font-3-4r tw-six q-pb-sm text-red-4 cursor-pointer" @click="dialog = true" v-if="missing">** Missing
      owner
      information
    </div>

    <div class="__c">
      <q-tab-panels class="_panel" :model-value="!!tab" animated>
        <q-tab-panel class="_panel" :name="false">

          <div class="_fw" v-if="!loading">
            <q-chip dense square class="tw-six bg-ir-light text-a9">25% + Owners</q-chip>
            <div v-if="!accountOwners?.length" class="font-3-4r text-ir-deep">If no owners with 25% ownership - provide at least 1 owner/stakeholder</div>
            <table>
              <tbody>

              <tr>
                <td></td>
                <td>Owner</td>
                <td>Share</td>
                <td>Address</td>
                <td>SSN</td>
                <td>DOB</td>
                <td>Email</td>
                <td>Phone</td>
              </tr>
              <tr v-if="!accountOwners?.length">
                <td class="q-pa-sm text-italic text-red">None Added</td>
              </tr>
              <tr v-for="(owner, i) in accountOwners || []" :key="`o-${i}`">
                <td>
                  <q-btn size="sm" dense flat icon="mdi-dots-vertical">
                    <q-menu>
                      <div class="w300 bg-white mw100">
                        <q-list separator>
                          <remove-item name="Owner" @remove="removeOwner(owner)"></remove-item>
                        </q-list>
                      </div>
                    </q-menu>
                  </q-btn>
                </td>
                <td @click="tab = owner">
                  {{ owner.name.firstName }} {{ owner.name.lastName }}
                </td>
                <td @click="tab = owner">
                  {{ dollarString(owner.responsibilities?.ownershipPercentage, '', 0) }}%
                </td>
                <td @click="tab = owner">
                  <q-icon color="green" name="mdi-check-circle" v-if="owner.address?.addressLine1"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = owner">
                  <q-icon color="green" name="mdi-check-circle" v-if="owner.governmentIDProvided"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = owner">
                  <q-icon color="green" name="mdi-check-circle" v-if="owner.birthDateProvided"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = owner">
                  <q-icon color="green" name="mdi-check-circle" v-if="owner.email"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = owner">
                  <q-icon color="green" name="mdi-check-circle" v-if="owner.phone"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
              </tr>
              </tbody>
            </table>
            <div class="q-py-sm">

              <q-chip color="ir-bg2" clickable @click="adding=!adding">
                <span class="q-mr-sm">Add Owner</span>
                <q-icon color="green" :name="adding ? 'mdi-minus' : 'mdi-plus'"></q-icon>
              </q-chip>

              <q-chip color="ir-bg2" clickable @click="selectOwner(person, false, true)" v-if="!accountOwners.map(a => a.email).includes(person.email)">
                <span class="q-mr-sm">Add {{ person.name }}</span>
                <q-icon color="blue" name="mdi-plus"></q-icon>
              </q-chip>
            </div>

            <div class="q-py-sm" v-if="!fullAccount?.profile?.business?.ownersProvided && accountOwners.length">
              <q-checkbox class="tw-six text-ir-deep" label="All owners of >=25% have been listed or - there are none and a stakeholder has been listed" @update:model-value="certifyOwners" :model-value="ownerCert"></q-checkbox>
            </div>

            <q-slide-transition>
              <div class="_fw" v-if="adding">

                <owner-picker
                    :existing="accountOwners"
                    @update:model-value="selectOwner($event, false, true)"
                    :org="fullOrg"
                    :people="owners"
                ></owner-picker>

              </div>
            </q-slide-transition>

            <div class="_fw q-pt-lg">
              <q-chip dense square class="tw-six bg-ir-light text-a9">Banking Controller</q-chip>
            </div>
            <table>
              <tbody>
              <tr>
                <td>Name</td>
                <td>Address</td>
                <td>SSN</td>
                <td>DOB</td>
                <td>Email</td>
                <td>Phone</td>
                <td>isController</td>
              </tr>
              <tr v-for="(rep, i) in accountControllers" :key="`rep-${i}`">
                <td @click="tab = rep">
                  <span v-if="rep.name">{{ rep.name.firstName }} {{ rep.name.lastName }}</span>
                  <span v-else class="__warn">Name Required &ast;</span>
                </td>
                <td @click="tab = rep">
                  <q-icon color="green" name="mdi-check-circle" v-if="rep.address?.addressLine1"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = rep">
                  <q-icon color="green" name="mdi-check-circle" v-if="rep.governmentIDProvided"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = rep">
                  <q-icon color="green" name="mdi-check-circle" v-if="rep.birthDateProvided"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = rep">
                  <q-icon color="green" name="mdi-check-circle" v-if="rep.email"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td @click="tab = rep">
                  <q-icon color="green" name="mdi-check-circle" v-if="rep.phone?.number"></q-icon>
                  <q-icon color="red" name="mdi-alert-box" v-else></q-icon>
                </td>
                <td>
                  <q-checkbox size="xs" :model-value="rep.responsibilities?.isController"
                              @update:model-value="toggleRep(rep, { isController: $event })"></q-checkbox>
                </td>
              </tr>
              <tr v-if="!accountControllers?.length">
                <td class="q-pa-sm text-italic text-red">None Added</td>
              </tr>
              </tbody>
            </table>
            <div class="q-py-sm">
              <q-chip color="ir-bg2" clickable @click="addingRep=!addingRep">
                <span class="q-mr-sm">Add Controller</span>
                <q-icon color="green" :name="addingRep ? 'mdi-minus' : 'mdi-plus'"></q-icon>
              </q-chip>

              <q-chip color="ir-bg2" clickable @click="selectOwner(person, true)"
                      v-if="!accountControllers.map(a => a.email).includes(person.email)">
                <span class="q-mr-sm">Add {{ person.name }}</span>
                <q-icon color="blue" name="mdi-plus"></q-icon>
              </q-chip>
            </div>

            <q-slide-transition>
              <div class="_fw" v-if="addingRep">

                <owner-picker
                    :existing="accountControllers"
                    title="Controller"
                    @update:model-value="selectOwner($event, true)"
                    :org="fullOrg"
                    :people="p$.data"
                ></owner-picker>

              </div>
            </q-slide-transition>

            <div class="q-pa-sm font-7-8r text-blue-8 text-italic text-center">Banking regulations require you to list all owners with 25% or greater ownership as well as 1 banking controller - an owner or officer with significant authority.
            </div>
          </div>
          <div v-else class="q-pa-md">
            <q-spinner size="40px" color="primary"></q-spinner>
          </div>
        </q-tab-panel>
        <q-tab-panel class="_panel" :name="true">
          <div class="_fw">
            <q-chip color="transparent" clickable icon="mdi-chevron-left" label="All Owners"
                    @click="tab = undefined"></q-chip>
          </div>
          <account-rep-form
              @update:model-value="closeTab"
              :person="tabPerson"
              :account="fullAccount"
              :org="fullOrg"
              :acct-person="tab"
              @update:account="updateAccount"></account-rep-form>
        </q-tab-panel>
      </q-tab-panels>

    </div>

    <common-dialog :model-value="!!completeOwner.length" @update:model-value="val => val ? '' : completeOwner = []" setting="smmd">
      <div class="_fw q-pa-md">
      <account-rep-form
          :person="completeOwner[0]"
          :account="fullAccount"
          :org="fullOrg"
          @update:model-value="selectOwner(...completeOwner)"
      ></account-rep-form>
      </div>
    </common-dialog>


  </div>
</template>

<script setup>
  import AccountRepForm from 'components/accounts/treasury/components/forms/AccountRepForm.vue';
  import OwnerPicker from 'components/accounts/cards/OwnerPicker.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {computed, ref, watch} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {usePpls} from 'stores/ppls';
  import {$errNotify, dollarString} from 'src/utils/global-methods';

  import {useBanking} from 'stores/banking';
  import {runMoovPersonMap} from '../../utils/moov-utils';
  import {loginPerson} from 'stores/utils/login';
  import {useOrgs} from 'stores/orgs';

  const { person } = loginPerson();

  const pplStore = usePpls();
  const bankStore = useBanking();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:account'])
  const props = defineProps({
    org: { required: true },
    account: { required: true },
    accountPeople: Object
  })

  const tab = ref(undefined)
  const loading = ref(false);
  const dialog = ref(false);
  const completeOwner = ref([]);

  const fullOrg = computed(() => props.org);
  const fullAccount = computed(() => props.account);

  const updateAccount = (val) => emit('update:account', val);

  const accountPpl = ref({ data: [] });
  const accountOwners = computed(() => accountPpl.value.data.filter(a => a.responsibilities?.isOwner));
  const accountControllers = computed(() => accountPpl.value.data.filter(a => a.responsibilities.isController))

  const ownerCert = ref(false);
  const certifyOwners = async () => {
    ownerCert.value = true;
    await bankStore.get(fullAccount.value.accountID, { banking: { moov: { method: 'account_update', args: [{ profile: { business: { ownersProvided: true }}}]}}})
        .catch(err => {
          console.log(`Error certifying owners added: ${err.message}`)
          $errNotify(`Error certifying - refresh and try again: ${err.message}`)
          ownerCert.value = false;
        })
    // console.log('acct response', acct)
    // if(acct) router.go();
  }

  const { h$: p$ } = HFind({
    store: pplStore,
    limit: computed(() => fullOrg.value?.owners?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: (fullOrg.value?.owners || []).filter(a => a.idService === 'ppls').map(a => a.id) }
        }
      }
    })
  })

  const refreshAccount = async () => {
    const acct = await bankStore.get(fullAccount.value.accountID, {
      banking: {
        moov: {
          method: 'account_get',
          args: []
        }
      }
    })
    if (acct) emit('update:account', acct);
  }

  const scrubPersons = async (tries = 0) => {
    // if(accountPpl.value.data?.length) {
    // if (p$.data.length) {
    // const ids = p$.data.map(a => (a.stripeAccounts || {})[fullAccount.value.accountID]?.id).filter(a => !!a);
    // for (const p of accountPpl.value.data) {
    //   const idx = ids.indexOf(p.id);
    //   if(idx === -1 && (!p.name.firstName || !p.name.lastName)) {
    //     bankStore.get(p.account, { banking: { stripe: { method: 'delete_person', args: [p.account, p.id]}}})
    //   }
    // }
    // refreshAccount()

    //   } else if (tries < 5) {
    //     setTimeout(() => {
    //       scrubPersons(tries + 1);
    //     }, 1000)
    //   }
    // }
  }
  watch(fullAccount, async (nv, ov) => {
    if (nv && nv.accountID !== ov?.accountID) {
      if (!props.accountPeople?.data?.length) accountPpl.value = await bankStore.get(nv.accountID, {
        banking: {
          moov: {
            method: 'list_representatives',
            args: [nv.accountID]
          }
        }
      });
      else accountPpl.value = props.accountPeople
      scrubPersons()
    }
  }, { immediate: true });

  const tabPerson = computed(() => {
    if (!tab.value) return undefined;
    return p$.data.filter(a => (a.moovAccounts || {})[tab.value.accountID]?.id === tab.value.accountID || (a.firstName === tab.value.name?.firstName && a.lastName === tab.value.name?.lastName))[0];
  })

  const closeTab = async () => {
    tab.value = undefined;
    const id = fullAccount.value.accountID
    const acct = await bankStore.get(id, { banking: { moov: { method: 'account_get', args: [id] } } })
    updateAccount(acct);
  }

  const owners = computed(() => {
    return p$.data.map(a => {
      const ownership = fullOrg.value?.owners?.filter(b => b.id === a._id)[0];
      return {
        ...a,
        ownership: { ...ownership, gte: ownership?.percent >= 25 }
      }
    }).filter(a => !(a.moovAccounts || {})[fullAccount.value?.accountID]).sort((a, b) => (b.ownership?.percent || 0) - (a.ownership?.percent || 0))
  })

  const missing = computed(() => fullAccount.value?.requirements?.currently_due?.some(a => a.includes('owners')));

  const adding = ref(false);
  const addingRep = ref(false);

  const removeOwner = async (o) => {
    await bankStore.get(o.account, {
      banking: {
        moov: {
          method: 'delete_representative',
          args: [fullAccount.value.accountID, o.representativeID, {
            responsibilities: {
              ...o.responsibilities,
              isController: false
            }
          }]
        }
      }
    })
  }

  const toggleRep = async (person, val) => {
    const otherControllers = !val ? [] : accountPpl.value.filter(a => a.responsibilities.isController && a.representativeID !== person.representativeID)
    await bankStore.get(fullAccount.value.accountID, {
      banking: {
        moov: {
          method: 'update_representative',
          args: [fullAccount.value.accountID, person.representativeID, {
            responsibilities: {
              ...person.responsibilities,
              isController: val
            }
          }]
        }
      }
    })
    /** Limit controllers to just 1 per account per Moov recommendations */
    for (let i = 0; i < otherControllers.length; i++) {
      await bankStore.get(fullAccount.value.accountID, {
        banking: {
          moov: {
            method: 'update_representative',
            args: [fullAccount.value.accountID, otherControllers[i].representativeID, {
              responsibilities: {
                ...otherControllers[i].responsibilities,
                isController: false
              }
            }]
          }
        }
      })
    }
    refreshAccount()

  }

  const selectOwner = async (val, rep, owner) => {
    if(completeOwner.value.length) completeOwner.value = [];
    else completeOwner.value = [val, rep, owner];
    if(owner && !(fullOrg.value.owners || []).map(a => a.id).includes(val._id)){
      const owner = {
        id: val._id,
        idService: 'ppls',
        percent: Math.max(0, 100 - (fullOrg.value.owners || []).reduce((acc, a) => acc + a.percent || 0, 0))
      }
      await orgStore.patch(fullOrg.value._id, { $addToSet: { owners: owner }})
          .catch(() => $errNotify('Error adding owner - try updating profile details, ssn, dob, phone number'))
    }
    loading.value = true;
    const { accountID } = fullAccount.value || {}
    //See if stripe person already exists for this person
    const existing = accountPpl.value.data?.filter(a => a.representativeID === (val.moovAccounts || {})[accountID]?.id || (a.name.firstName === val.firstName && a.name.lastName === val.lastName))[0]
    // console.log('here', existing);
    if (existing?.representativeID) {
      const isController = existing.responsibilities?.isController || rep;
      const isOwner = existing.responsibilities?.isOwner || owner;
      // console.log('select rep', isController, rep, isOwner, owner)
      /**make sure ownership and representative status is up to date*/
      await bankStore.get(accountID, {
        banking: {
          moov: {
            method: 'update_representative',
            args: [accountID, existing.representativeID, {
              responsibilities: {
                ...existing.responsibilities,
                isOwner,
                isController,
                ownershipPercentage: isOwner ? (fullOrg.value.owners || []).filter(a => a.id === val._id)[0]?.percent || 0 : 0,
                jobTitle: (fullOrg.value.owners || []).filter(a => a.id === val._id)[0]?.position || existing.responsibilities.jobTitle || 'owner'
              }
            }]
          }
        }
      })
          .catch(err => {
            console.error(`Error updating representative: ${err.message}`);
          })

      //if moov representative id hasn't been added - add it
      if (!(val.moovAccounts || {})[accountID]?.id) await pplStore.patch(val._id, { $set: { [`moovAccounts.${accountID}`]: { id: existing.representativeID } } })
          .catch(err => {
            console.error(`Error adding moov account to person: ${err.message}`);
          })
          .catch(err => {
            console.error(`Error adding moov account to person: ${err.message}`);
          })
      loading.value = false;
    } else {
      //if no matching moov person found, creat a new moov person
      let accountPerson = undefined;
      if (rep) accountPerson = { responsibilities: { isOwner: owner, isController: rep } }
      const newRep = runMoovPersonMap({ person: val, org: fullOrg.value, accountPerson, minPercent: 25 })
      await bankStore.get(val._id, {
        banking: {
          moov: {
            method: 'create_representative',
            args: [accountID, val._id, {...newRep}]
          }
        }
      })
          .catch(err => {
            console.error(err.message);
          })
      loading.value = false
    }
    refreshAccount()
  }


</script>

<style lang="scss" scoped>
  .__c {
    padding: 10px 15px;
    background: white;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;

    tr {
      cursor: pointer
    }

    tr:first-child {
      color: #999;
      font-weight: 600;
      font-size: .8rem;
    }

    td {
      border-bottom: solid .3px #999;
      padding: 5px 10px;
    }
  }

  .__warn {
    font-size: .7rem;
    color: var(--q-s7);
  }
</style>
