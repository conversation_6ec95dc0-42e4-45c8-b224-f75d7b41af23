<template>
  <div class="_fw _form_grid _f_g_r">
    <div class="_form_label">Terms of Service</div>
    <div class="q-pa-sm">
      <div class="font-14 q-pb-sm">
        <div>We are working with <span class="alt-font tw-six">Moov</span> to make payments
          smooth and secure for your plan
        </div>
        <div id="drop_zone"></div>

        <div class="q-py-md row">
          <q-btn v-if="tokenReady && !account?.termsOfService?.acceptedDate" @click="getTosToken" color="ir-text" no-caps class="tw-six"
                 label="Accept Terms"></q-btn>
          <q-checkbox left-label :label="`Accepted: ${$dateDisplay(acceptedDate, '', 'Not Accepted Yet')}`"
                      :model-value="true" v-else-if="account.termsOfService?.acceptedDate"></q-checkbox>
        </div>

        <!--        <div>Confirm that you have read and agree to <PERSON><PERSON>'s <span class="text-accent tw-six"><a target="_blank"-->
        <!--                                                                                                href="https://moov.io/legal/platform-agreement">Platform Agreement</a></span>-->
        <!--          and <span class="text-accent tw-six"><a target="_blank" href="https://moov.io/legal/privacy-policy/">Privacy Policy</a></span>-->
      </div>
    </div>
  </div>


</template>

<script setup>
  import {computed, ref, watch} from 'vue';
  import {useBanking} from 'stores/banking';
  import {$dateDisplay} from 'src/utils/global-methods';
  import {termsGen} from 'components/accounts/utils/terms-gen';
  import {useRouter} from 'vue-router';
  // import Moov from '@moovio/moov-js'

  const { stamp } = termsGen();

  const bankStore = useBanking();
  const router = useRouter();

  const emit = defineEmits(['accept', 'update:tos-token', 'update:account', 'update:manual-tos'])

  const moov = ref()

  const props = defineProps({
    manualTos: Object,
    tosToken: String,
    account: { required: true }
  })

  const acceptedDate = computed(() => {
    if (!props.account?.termsOfService?.acceptedDate) return new Date();
    else return new Date(props.account.termsOfService.acceptedDate);
  })

  const clientToken = ref()
  const tokenReady = ref(false);


  const tokenGenerated = ref(false);
  const genToken = async (tries = 0, token) => {
    if(tokenGenerated.value) return;
    if(!props.account?.accountID){
      if(tries < 10) setTimeout(() => genToken(tries + 1), 250)
      else return;
    }
    if(props.account?.termsOfService?.acceptedDate) return;
    const manual = {
      acceptedDate: new Date(stamp.value.date || new Date()),
      acceptedIP: stamp.value.ip,
      acceptedUserAgent: stamp.value.user_agent,
      acceptedDomain: window.location.host || 'commoncare.org'
    }
    if (!document.getElementById('moovjs-script')) {
      const scriptTag = document.createElement('script');
      scriptTag.type = 'text/javascript';
      scriptTag.src = 'https://js.moov.io/v1?min=v0.6.12';
      scriptTag.id = 'moovjs-script';
      document.getElementsByTagName('head')[0].appendChild(scriptTag);
      scriptTag.onload = () => genToken()
      scriptTag.onerror = () => {
        if (tries < 10) setTimeout(() => genToken(tries + 1), 100)
      }
    } else {
      let res = { token }
      if (!token) {
        const args = [{ scope: `/accounts.write /accounts/${props.account.accountID}/profile.write`}];
        res = await bankStore.get(props.account?.accountID || new Date().getTime().toString(), {
          banking: {
            moov: {
              method: 'get_drop_token',
              // method: 'get_tos_token',
              args
            }
          }
        })
            .catch(err => console.error(`Error getting tos token: ${err.message}`))
      }
      if (res?.token) {
        clientToken.value = res.token;
        // console.log('got token', res)
        moov.value = Moov(res.token);
        const el = document.getElementById('drop_zone');
        const tos = document.createElement('moov-terms-of-service');
        tos.token = res.token;
        tos.textColor = 'rgb(0, 0, 0)';       // Black text
        tos.linkColor = 'rgb(0, 0, 238)';     // A blue color for links
        tos.backgroundColor = 'rgb(255, 255, 255)'; // White background
        tos.fontSize = '14px';
        // tos.addEventListener('termsOfServiceTokenReady', (event) => {
        //   console.log('Token ready:')
        //   tokenReady.value = true;
        // });

        // tos.addEventListener('termsOfServiceTokenError', (event) => {
        //   console.error('ToS token error:', event.detail);
        // });

        // tos.addEventListener('accepted', (event) => {
        //   console.log('ToS accepted');
        // });
        tos.onTermsOfServiceTokenReady = (token) => {
          tokenReady.value = token;
        }
        tos.onTermsOfServiceTokenError = (err) => console.log('error property', err);
        // tos.customActionCopy = 'By pressing accept';
        // tos.style.height = '300px'
        // tos.style.width = '500px'
        // tos.textColor = 'black'
        // tos.linkColor = '#000070'
        // tos.backgroundColor = 'white';
        // tos.fontSize = '1rem';

        if(tokenGenerated.value) return
        el.appendChild(tos);
        tokenGenerated.value = true;
        emit('update:tos-token', { token: res.token });
        emit('update:manual-tos', manual);
      } else if (tries < 10) setTimeout(() => genToken(tries + 1), 250)
      else {
        emit('update:manual-tos', manual);
      }
    }
  }
  const getTosToken = async () => {
    emit('accept', clientToken.value);
    if (props.account?.accountID) {
      // const token = await moov.value.accounts.getTermsOfServiceToken()

      // const args = [{ termsOfService: { token: tokenReady.value } }]
      //
      // await bankStore.get(props.account.accountID, {
      //   banking: {
      //     moov: {
      //       method: 'account_update',
      //       args
      //     }
      //   }
      // })
      // const updated = await moov.value.accounts.patch({ accountID: props.account.accountID, termsOfService: token })
      const updated = await moov.value.accounts.acceptTermsOfService({ accountID: props.account.accountID })
      // const updated = await moov.value.accounts.acceptTermsOfService({ accountID: props.account.accountID })
      if(updated) router.go(0)
      // console.log('tos token', updated);
      // const args = [{ termsOfService: { token: clientToken.value} }]
      // console.log('got updated??', args);
      // await bankStore.get(props.account.accountID, {
      //   banking: {
      //     moov: {
      //       method: 'account_update',
      //       args
      //     }
      //   }
      // })
    }
  }

  watch(() => props.account, async (nv, ov) => {
    if (nv?.accountID && nv.accountID !== ov?.accountID) {
      setTimeout(async () => {
        await genToken(0)
      }, 500)
    } else setTimeout(() => {
      if (!props.tosToken?.token && !props.account?.accountID) genToken(0)
    }, 1500)
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  a {
    text-decoration: none;
    color: var(--q-accent);
  }

  #drop_zone {
    width: 100%;
  }

  :root {
    --moov-color-background: #111111;
    --moov-color-background-secondary: #222222;
    --moov-color-background-tertiary: #333333;
    --moov-color-primary: #77D656;
    --moov-color-secondary: #79F0AA;
    --moov-color-tertiary: #4F4F4F;
    --moov-color-info: #94CBFF;
    --moov-color-warn: #F2994A;
    --moov-color-danger: #EB5757;
    --moov-color-success: #77D656;
    --moov-color-low-contrast: #969696;
    --moov-color-medium-contrast: #E0E0E0;
    --moov-color-high-contrast: #FFFFFF;
    --moov-color-graphic-1: #79F0AA;
    --moov-color-graphic-2: #D194E9;
    --moov-color-graphic-3: #68B2FD;
    --moov-radius-small: .375rem;
    --moov-radius-large: .5rem;
  }
</style>
