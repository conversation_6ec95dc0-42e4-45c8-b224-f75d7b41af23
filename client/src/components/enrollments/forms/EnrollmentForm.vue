<template>
  <div class="_fw">
    <div class="_form_grid">
      <div class="_form_label">Description</div>
      <div class="q-pa-sm">
        <q-input @update:model-value="autoSave" v-model="form.description" autogrow filled placeholder="Enrollment description..."></q-input>
      </div>

      <div class="_form_label">Enrollment<br>Type</div>
      <div class="q-pa-sm">
        <q-checkbox @update:model-value="autoSave" v-model="form.open_enroll" label="Open"></q-checkbox>
        <q-checkbox :model-value="!form.open_enroll" @update:model-value="form.open_enroll = !$event, autoSave()"
                    label="Special/Limited"></q-checkbox>
      </div>

      <div class="_form_label">Open/Close Dates</div>
      <div class="q-pa-sm row items-center">
        <div class="col-6 q-px-xs">
          <inline-date @update:model-value="autoSave" input-class="num-font tw-six" label="Open Date" v-model="form.open"></inline-date>
        </div>
        <div class="col-6 q-px-xs">
          <inline-date @update:model-value="autoSave" input-class="num-font tw-six" label="Close Date" v-model="form.close"></inline-date>
        </div>
      </div>

      <template v-if="!form.open_enroll">
        <div class="_form_label"> Who can<br>Enroll?</div>

        <div class="q-pa-sm">

          <q-table
              dense
              id="I_S"
              flat
              :rows="whiteListGroups.map(a => getGroup(a))"
              :columns="gCols"
              hide-pagination
              :selection="whiteListGroups?.length ? 'multiple' : undefined"
              row-key="_id"
              v-model:selected="removeList.groups"
          >
            <template v-slot:no-data>
              <div class="q-pa-sm text-italic">No groups added</div>
            </template>
            <template v-slot:top>
              <div class="flex items-center q-px-sm">
                <div class="font-1r tw-six">Eligible Groups</div>
                <q-btn dense flat no-caps @click="addDialog = 'groups'">
                  <q-icon name="mdi-plus" color="secondary"></q-icon>
                </q-btn>
              </div>
            </template>
            <template v-slot:bottom>
              <div class="row _fw">
                <remove-button :label="`Remove ${removeList.groups.length}`" flat size="sm" v-if="removeList.groups?.length" :name="removeList.groups.length" no-caps @remove="remove('groups')">
                </remove-button>
                <q-space></q-space>
                <div class="font-7-8r text-grey-8">{{ whiteListGroups?.length ? 1 : 0 }} - {{ whiteListGroups?.length || 0 }} of
                  {{ whiteListGroups.length }}
                </div>
              </div>
            </template>
          </q-table>

          <q-separator class="q-my-sm"></q-separator>

          <q-table
              dense
              id="I_S"
              flat
              :rows="p$.data"
              :columns="pCols"
              virtual-scroll
              @virtual-scroll="senseScrollLoad"
              hide-pagination
              :selection="p$.data?.length ? 'multiple' : undefined"
              row-key="_id"
              v-model:selected="removeList.ppls"
          >
            <template v-slot:no-data>
              <div class="q-pa-sm text-italic">Nobody added</div>
            </template>
            <template v-slot:top>
              <div class="flex items-center q-px-sm">
                <div class="font-1r tw-six">Eligible People</div>
                <q-btn dense flat no-caps @click="addDialog = 'ppls'">
                  <q-icon name="mdi-plus" color="primary"></q-icon>
                </q-btn>
              </div>
            </template>
            <template v-slot:bottom>
              <div class="row _fw">
                <remove-button :label="`Remove ${removeList.ppls.length}`" flat size="sm" v-if="removeList.ppls?.length" :name="removeList.ppls.length" no-caps @remove="remove('ppls')">
                </remove-button>
                <q-space></q-space>
                <div class="font-7-8r text-grey-8">{{ p$.data?.length ? 1 : 0 }} - {{ p$.data?.length || 0 }} of
                  {{ p$.total }}
                </div>
              </div>
            </template>
          </q-table>

        </div>
      </template>
    </div>

    <common-dialog :model-value="!!addDialog" @update:model-value="toggleAdd">
      <div class="q-pa-lg bg-white">
        <div class="tw-six">Add {{ addDialog === 'ppls' ? 'people' : 'groups' }} to enrollment {{ planKey }}</div>
        <q-input v-model="search.text" placeholder="Search...">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-slide-transition>
          <div class="__list __results" v-if="addData">
            <q-list separator>
              <q-item
                  clickable
                  @click="add(p, addDialog)"
                  v-for="(p, i) in addData"
                  :key="`ps-${i}`"
              >
                <q-item-section>
                  <q-item-label>{{getKey(p)}}{{p.name}}</q-item-label>
                  <q-item-label caption>{{p.email || p.description || ''}}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item-label header v-if="!addData?.length">No eligible {{addDialog === 'ppls' ? 'People' : 'Groups'}} found</q-item-label>
            </q-list>
          </div>
        </q-slide-transition>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import RemoveButton from 'components/common/buttons/RemoveButton.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {planPeople} from 'components/plans/utils/people';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind, hInfiniteScroll} from 'src/utils/hFind';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {usePpls} from 'stores/ppls';

  const pplStore = usePpls()

  const props = defineProps({
    plan: { required: true },
    planKey: { required: true }
  })

  const formFn = (defs) => {
    const open = new Date();
    return {
      description: '',
      open_enroll: _get((props.planKey || '*')?.split('_'), [1]) === '0',
      open,
      close: new Date(open.getTime() + (30 * 24 * 60 * 60 * 1000)),
      enrolled: 0,
      ...defs
    }
  }

  const getKey = (val) => {
    if(!val.key) return '';
    return val.key.split(':')[0].split('_').map(a => $capitalizeFirstLetter(a)).join(' ') + ' - '
  }

  const whiteListPpl = computed(() => _get(props.plan, ['enrollments', props.planKey, 'ppls'], []))

  const whiteListGroupIds = computed(() => _get(props.plan, ['enrollments', props.planKey, 'groups'], []))
  const whiteListGroups = computed(() => (groupData.h$.data || []).filter(a => whiteListGroupIds.value.includes(a._id)))

  const { searchQ, search } = HQuery({
    query: computed(() => {
      return {
        _id: { $nin: whiteListPpl }
      }
    })
  })

  const pplQuery = computed(() => {
    const q = searchQ.value;
    if (whiteListPpl.value?.length > 0) q._id = { $nin: whiteListPpl.value }
    return q
  })

  const {
    fullPlan,
    mbrs,
    groupData,
    planStore
  } = planPeople(computed(() => props.plan), { query: pplQuery })

  const form = ref(formFn());

  let timeout;
  const autoSave = () => {
    if(timeout) window.clearTimeout(timeout);
    timeout = window.setTimeout(async () => {
      const patched = planStore.patch(fullPlan.value._id, { $set: { [`enrollments.${props.planKey}`]: form.value }});
      form.value = formFn(patched.enrollments[props.planKey]);
    }, 2000);
  }

  const { h$: p$ } = HFind({
    store: pplStore,
    limit: ref(25),
    params: computed(() => {
      return {
        query: {
          _id: { $in: whiteListPpl.value }
        }
      }
    })
  })

  const { senseScrollLoad } = hInfiniteScroll({ h$: p$, loadNum: 10 })

  const remove = async (path) => {
    const list = removeList.value[path].map(a => a._id);
    const v = await planStore.patch(fullPlan.value._id, { $pull: { [`enrollments.${props.planKey}.${path}`]: { $in: list } } })
    planStore.patchInStore(fullPlan.value._id, v)
    form.value = formFn(fullPlan.value.enrollments[props.planKey]);
    removeList.value[path] = [];
  }

  const addList = ref([]);
  const removeList = ref({
    ppls: [],
    groups: []
  })
  const addDialog = ref(false);
  const addData = computed(() => addDialog.value === 'ppls' ? mbrs.h$.data.map(a => a._fastjoin?.person) : (groupData.h$.data || []).map(a => getGroup(a)).filter(a => !whiteListGroupIds.value?.includes(a._id) && a.name?.toLowerCase().includes(search.value.text?.toLowerCase())));

  const toggleAdd = async (val) => {
    if (!val) {
      if (addList.value?.length) {
        await planStore.patch(fullPlan.value._id, { $addToSet: { [`enrollments.${props.planKey}.${addDialog.value}`]: { $each: addList.value } } })
      }
      search.value.text = '';
      addDialog.value = undefined;
    }
  }

  const getGroup = (grp) => {
    const orgName = grp.key?.split(':')[0].split('_').map(a => $capitalizeFirstLetter(a)).join(' ');
    return {
      ...grp,
      orgName
    }
  }
  const add = (val, path) => {
    const list = [...path === 'ppls' ? whiteListPpl.value : whiteListGroups.value, val._id];
    addList.value.push(val._id);
    planStore.patchInStore(fullPlan.value._id, {
      enrollments: {
        ...fullPlan.value.enrollments,
        [props.planKey]: { ...fullPlan.value.enrollments[props.planKey], [path]: list }
      }
    })
    form.value = formFn(fullPlan.value.enrollments[props.planKey]);
  }

  const pCols = computed(() => {
    return [
      {
        label: 'Name',
        name: 'name',
        field: 'name'
      }
    ].map(a => {
      return {
        sortable: true,
        align: 'left',
        ...a
      };
    })
  })

  const gCols = computed(() => {
    return [
      {
        label: 'Organization',
        name: 'org',
        field: 'orgName'
      },
      {
        label: 'Group',
        name: 'name',
        field: 'name'
      }
    ].map(a => {
      return {
        sortable: true,
        align: 'left',
        ...a
      };
    })
  })

  watch(fullPlan, (nv) => {
    if(nv) form.value = formFn(nv.enrollments[props.planKey]);
  }, { immediate: true })

  onMounted(() => {
    autoSave()
  })

</script>

<style lang="scss" scoped>
  .__list #I_S {
    width: 100%;
    max-height: 500px;
    min-height: 50px;
  }

  .__results {
    min-height: 5px;
    transition: all .15s;
  }
</style>
